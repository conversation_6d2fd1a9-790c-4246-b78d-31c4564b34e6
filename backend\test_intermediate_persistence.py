#!/usr/bin/env python3
"""
測試中間結果持久化功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus, TaskPriority
from app.services.executors.regulation_compliance_executor import RegulationComplianceExecutor
from app.services.analysis_task_service import AnalysisTaskService
import logging
from datetime import datetime
import uuid
import json

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_intermediate_persistence():
    """測試中間結果持久化功能"""
    
    # 獲取數據庫會話
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        logger.info("🚀 開始測試中間結果持久化功能")
        
        # 創建模擬任務對象
        task_id = str(uuid.uuid4())
        mock_task = AnalysisTask(
            task_id=task_id,
            purchase_id="1",  # 使用現有的購案ID
            task_type=TaskType.REGULATION_COMPLIANCE,
            task_name="法規比對持久化測試",
            description="測試法規比對執行器的中間結果持久化功能",
            status=TaskStatus.PENDING,
            priority=TaskPriority.HIGH,
            progress=0,
            estimated_duration=600,
            config={
                "step": "regulation_compliance_check",
                "test_mode": True,
                "enable_persistence": True
            },
            created_time=datetime.utcnow()
        )
        
        logger.info(f"📋 創建模擬任務: {mock_task.task_id}")
        
        # 創建任務服務和執行器
        task_service = AnalysisTaskService(db)
        executor = RegulationComplianceExecutor(db)
        
        logger.info("⚡ 開始執行法規比對任務（含中間結果持久化）")
        
        # 執行任務
        result = await executor.execute(mock_task)
        
        logger.info("✅ 法規比對任務執行完成")
        logger.info(f"📄 執行結果狀態: {result.get('status')}")
        
        # 檢查中間結果是否已保存
        logger.info("\n🔍 檢查中間結果持久化情況:")
        
        if result.get('intermediate_results'):
            intermediate_results = result['intermediate_results']
            
            logger.info(f"📊 發現 {len(intermediate_results)} 個中間結果:")
            
            for step_name, step_data in intermediate_results.items():
                logger.info(f"  - {step_name}: {type(step_data).__name__}")
                if isinstance(step_data, dict):
                    if 'key_clauses' in step_data:
                        logger.info(f"    關鍵條款數量: {len(step_data.get('key_clauses', []))}")
                    if 'compliance_score' in step_data:
                        logger.info(f"    符合度分數: {step_data.get('compliance_score')}")
                    if 'violations' in step_data:
                        logger.info(f"    違規項目數量: {len(step_data.get('violations', []))}")
        
        # 檢查任務配置中的中間結果
        logger.info("\n📋 檢查任務配置中的中間結果:")
        
        if mock_task.config and 'intermediate_results' in mock_task.config:
            config_results = mock_task.config['intermediate_results']
            logger.info(f"📊 任務配置中保存了 {len(config_results)} 個中間結果:")
            
            for step_name, step_info in config_results.items():
                timestamp = step_info.get('timestamp', 'N/A')
                step_order = step_info.get('step_order', 'N/A')
                logger.info(f"  - {step_name} (順序: {step_order}, 時間: {timestamp})")
                
                # 顯示部分數據內容
                step_data = step_info.get('data', {})
                if isinstance(step_data, dict):
                    if 'key_clauses' in step_data:
                        clauses = step_data.get('key_clauses', [])
                        logger.info(f"    關鍵條款: {clauses[:3]}{'...' if len(clauses) > 3 else ''}")
                    if 'compliance_score' in step_data:
                        logger.info(f"    符合度分數: {step_data.get('compliance_score')}")
        
        # 檢查文件系統中的中間結果
        logger.info("\n📁 檢查文件系統中的中間結果:")
        
        results_dir = Path("./analysis_results") / mock_task.purchase_id / mock_task.task_id
        if results_dir.exists():
            result_files = list(results_dir.glob("*.json"))
            logger.info(f"📊 發現 {len(result_files)} 個結果文件:")
            
            for result_file in result_files:
                logger.info(f"  - {result_file.name} ({result_file.stat().st_size} bytes)")
                
                # 讀取並顯示文件內容摘要
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        file_data = json.load(f)
                    
                    step_name = file_data.get('step_name', 'unknown')
                    timestamp = file_data.get('timestamp', 'N/A')
                    logger.info(f"    步驟: {step_name}, 時間: {timestamp}")
                    
                    data = file_data.get('data', {})
                    if isinstance(data, dict) and data:
                        logger.info(f"    數據鍵: {list(data.keys())[:5]}")
                        
                except Exception as e:
                    logger.error(f"    讀取文件失敗: {e}")
        else:
            logger.warning("📁 未找到結果目錄")
        
        # 測試中間結果載入功能
        logger.info("\n🔄 測試中間結果載入功能:")
        
        clauses_result = await executor._load_intermediate_result(mock_task, "clauses_analysis")
        if clauses_result:
            logger.info("✅ 成功載入條款分析結果")
            if isinstance(clauses_result, dict) and 'key_clauses' in clauses_result:
                logger.info(f"  關鍵條款數量: {len(clauses_result.get('key_clauses', []))}")
        else:
            logger.warning("⚠️ 未能載入條款分析結果")
        
        compliance_result = await executor._load_intermediate_result(mock_task, "compliance_check")
        if compliance_result:
            logger.info("✅ 成功載入法規比對結果")
            if isinstance(compliance_result, dict) and 'compliance_score' in compliance_result:
                logger.info(f"  符合度分數: {compliance_result.get('compliance_score')}")
        else:
            logger.warning("⚠️ 未能載入法規比對結果")
        
        logger.info("🎉 中間結果持久化測試完成！")
        
    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_intermediate_persistence())
