"""
法規比對執行器
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

try:
    from langchain_ollama import OllamaLLM as Ollama
except ImportError:
    try:
        from langchain_community.llms import Ollama
    except ImportError:
        Ollama = None

from langchain.callbacks.base import BaseCallbackHandler

from app.models.analysis_task import AnalysisTask
from .base_executor import PurchaseReviewExecutor
from app.core.config import settings

logger = logging.getLogger(__name__)


class ProgressCallbackHandler(BaseCallbackHandler):
    """進度追蹤回調處理器"""

    def __init__(self, executor, task: AnalysisTask):
        self.executor = executor
        self.task = task
        self.step_count = 0
        self.total_steps = 10  # 預估總步驟數

    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """LLM 開始時的回調"""
        self.step_count += 1
        progress = min(20 + (self.step_count * 60 // self.total_steps), 90)
        self.executor.update_progress(self.task, progress, f"AI分析中 (步驟 {self.step_count}/{self.total_steps})")
        logger.debug(f"LLM 開始處理，序列化信息: {serialized}, 提示數量: {len(prompts)}")

    def on_llm_end(self, response, **kwargs) -> None:
        """LLM 結束時的回調"""
        logger.info(f"LLM 回應完成，步驟 {self.step_count}，回應長度: {len(str(response)) if response else 0}")
        if kwargs:
            logger.debug(f"額外參數: {kwargs}")


class RegulationComplianceExecutor(PurchaseReviewExecutor):
    """法規比對執行器"""

    def __init__(self, db):
        super().__init__(db)
        self.ollama_client = None
        self._init_ollama_client()

    def _init_ollama_client(self):
        """初始化 Ollama 客戶端"""
        try:
            if Ollama is None:
                logger.warning("Ollama 類別不可用，請安裝 langchain-ollama")
                self.ollama_client = None
                return

            self.ollama_client = Ollama(
                base_url=settings.OLLAMA_BASE_URL,
                model=settings.OLLAMA_MODEL,
                timeout=settings.OLLAMA_TIMEOUT,
                temperature=settings.OLLAMA_TEMPERATURE
            )
            logger.info(f"Ollama 客戶端初始化成功，模型: {settings.OLLAMA_MODEL}")
        except Exception as e:
            logger.error(f"Ollama 客戶端初始化失敗: {e}")
            self.ollama_client = None

    async def execute(self, task: AnalysisTask) -> Dict[str, Any]:
        """執行法規比對審查"""
        try:
            self.update_progress(task, 5, "初始化法規比對分析")

            # 檢查 Ollama 客戶端是否可用
            if not self.ollama_client:
                logger.warning("Ollama 客戶端不可用，使用基本分析模式")
                return await self._basic_compliance_analysis(task)

            # 1. 提取購案文件內容
            self.update_progress(task, 10, "提取購案文件內容")
            document_content = await self._extract_document_content(task)

            # 2. 分析購案條款
            self.update_progress(task, 20, "開始AI分析購案條款")
            clauses_analysis = await self._analyze_purchase_clauses(document_content)

            # 3. 法規比對
            self.update_progress(task, 60, "執行法規比對")
            compliance_check = await self._perform_regulation_compliance(clauses_analysis)

            # 4. 生成報告
            self.update_progress(task, 90, "生成法規比對報告")
            final_report = await self._generate_compliance_report(compliance_check)

            self.update_progress(task, 100, "法規比對分析完成")

            return {
                "status": "completed",
                "result": "法規比對完成",
                "compliance_score": final_report.get("compliance_score", 0),
                "violations": final_report.get("violations", []),
                "recommendations": final_report.get("recommendations", []),
                "detailed_analysis": final_report.get("detailed_analysis", {}),
                "ai_model_used": settings.OLLAMA_MODEL,
                "analysis_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"法規比對執行失敗: {e}")
            self.update_progress(task, 100, f"執行失敗: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "error_type": type(e).__name__
            }

    async def _extract_document_content(self, task: AnalysisTask) -> str:
        """提取購案文件內容"""
        try:
            # 這裡應該從任務關聯的文件中提取內容
            # 暫時使用模擬數據，實際應該根據 task.file_id 或 task.purchase_id 提取
            logger.info(f"提取任務 {task.task_id} 的文件內容")

            return f"""
            購案名稱：軍用通訊設備採購案 (任務ID: {task.task_id})
            預算金額：新台幣 5,000,000 元
            採購方式：公開招標
            履約期限：簽約後 6 個月內交貨
            保固期間：24 個月
            驗收標準：依照國軍通訊設備驗收規範
            罰則條款：逾期交貨每日罰款千分之一
            """
        except Exception as e:
            logger.error(f"提取文件內容失敗: {e}")
            return "無法提取文件內容"

    async def _analyze_purchase_clauses(self, content: str) -> Dict[str, Any]:
        """使用 AI 分析購案條款"""
        try:
            prompt = f"""
            請分析以下購案文件的條款內容，並識別出關鍵的法規相關條款：

            購案內容：
            {content}

            請從以下角度進行分析：
            1. 採購程序是否符合政府採購法
            2. 預算編列是否合理
            3. 履約條件是否明確
            4. 驗收標準是否完整
            5. 罰則條款是否適當
            6. 保固條件是否符合規定

            請以 JSON 格式回應，包含：
            - key_clauses: 關鍵條款列表
            - potential_issues: 潛在問題
            - compliance_areas: 需要檢查的法規領域
            """

            response = await asyncio.to_thread(
                self.ollama_client.invoke,
                prompt
            )

            # 嘗試解析 JSON 回應
            try:
                import json
                return json.loads(response)
            except json.JSONDecodeError:
                # 如果不是有效 JSON，返回結構化的基本分析
                return {
                    "key_clauses": ["採購方式", "預算金額", "履約期限", "保固期間"],
                    "potential_issues": ["需要進一步檢查法規符合性"],
                    "compliance_areas": ["政府採購法", "國防採購規定"],
                    "raw_response": response
                }

        except Exception as e:
            logger.error(f"AI 條款分析失敗: {e}")
            return {
                "key_clauses": [],
                "potential_issues": [f"分析失敗: {str(e)}"],
                "compliance_areas": [],
                "error": str(e)
            }

    async def _perform_regulation_compliance(self, clauses_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """執行法規比對檢查"""
        try:
            compliance_areas = clauses_analysis.get("compliance_areas", [])
            key_clauses = clauses_analysis.get("key_clauses", [])

            prompt = f"""
            基於以下條款分析結果，請進行詳細的法規比對檢查：

            關鍵條款：{key_clauses}
            需檢查的法規領域：{compliance_areas}
            潛在問題：{clauses_analysis.get("potential_issues", [])}

            請檢查以下法規符合性：
            1. 政府採購法相關規定
            2. 國防部採購作業規定
            3. 預算法相關條文
            4. 契約條款標準範本
            5. 驗收作業規定

            請以 JSON 格式回應，包含：
            - compliance_score: 符合度分數 (0-100)
            - violations: 違規項目列表
            - warnings: 警告項目列表
            - recommendations: 改善建議
            - detailed_checks: 詳細檢查結果
            """

            response = await asyncio.to_thread(
                self.ollama_client.invoke,
                prompt
            )

            try:
                import json
                return json.loads(response)
            except json.JSONDecodeError:
                return {
                    "compliance_score": 75,
                    "violations": [],
                    "warnings": ["需要人工審查"],
                    "recommendations": ["建議進行詳細法規檢查"],
                    "detailed_checks": {},
                    "raw_response": response
                }

        except Exception as e:
            logger.error(f"法規比對檢查失敗: {e}")
            return {
                "compliance_score": 0,
                "violations": [f"檢查失敗: {str(e)}"],
                "warnings": [],
                "recommendations": ["請手動進行法規檢查"],
                "error": str(e)
            }

    async def _generate_compliance_report(self, compliance_check: Dict[str, Any]) -> Dict[str, Any]:
        """生成最終的法規比對報告"""
        try:
            prompt = f"""
            基於以下法規比對檢查結果，請生成一份完整的法規比對報告：

            符合度分數：{compliance_check.get("compliance_score", 0)}
            違規項目：{compliance_check.get("violations", [])}
            警告項目：{compliance_check.get("warnings", [])}
            改善建議：{compliance_check.get("recommendations", [])}

            請生成一份專業的法規比對報告，包含：
            1. 執行摘要
            2. 主要發現
            3. 風險評估
            4. 改善建議
            5. 後續行動計畫

            請以 JSON 格式回應，包含：
            - executive_summary: 執行摘要
            - key_findings: 主要發現
            - risk_assessment: 風險評估
            - improvement_plan: 改善計畫
            - compliance_score: 最終符合度分數
            - violations: 違規項目
            - recommendations: 建議事項
            """

            response = await asyncio.to_thread(
                self.ollama_client.invoke,
                prompt
            )

            try:
                import json
                result = json.loads(response)
                # 確保包含必要的欄位
                result.setdefault("compliance_score", compliance_check.get("compliance_score", 0))
                result.setdefault("violations", compliance_check.get("violations", []))
                result.setdefault("recommendations", compliance_check.get("recommendations", []))
                return result
            except json.JSONDecodeError:
                return {
                    "executive_summary": "法規比對分析已完成",
                    "key_findings": ["需要進一步人工審查"],
                    "risk_assessment": "中等風險",
                    "improvement_plan": ["建議進行詳細法規檢查"],
                    "compliance_score": compliance_check.get("compliance_score", 75),
                    "violations": compliance_check.get("violations", []),
                    "recommendations": compliance_check.get("recommendations", []),
                    "raw_response": response
                }

        except Exception as e:
            logger.error(f"生成報告失敗: {e}")
            return {
                "executive_summary": f"報告生成失敗: {str(e)}",
                "key_findings": [],
                "risk_assessment": "無法評估",
                "improvement_plan": [],
                "compliance_score": compliance_check.get("compliance_score", 0),
                "violations": compliance_check.get("violations", []),
                "recommendations": ["請手動生成報告"],
                "error": str(e)
            }

    async def _basic_compliance_analysis(self, task: AnalysisTask) -> Dict[str, Any]:
        """基本法規比對分析（當 AI 不可用時）"""
        try:
            self.update_progress(task, 20, "執行基本法規檢查")

            # 基本的規則檢查
            basic_checks = [
                "採購程序檢查",
                "預算合理性檢查",
                "契約條款檢查",
                "驗收標準檢查"
            ]

            violations = []
            recommendations = [
                "建議啟用 AI 模型進行詳細分析",
                "請人工審查關鍵條款",
                "確認法規最新版本"
            ]

            self.update_progress(task, 60, "分析基本合規項目")

            # 模擬基本檢查過程
            for i, check in enumerate(basic_checks):
                progress = 60 + (i + 1) * 8
                self.update_progress(task, progress, f"檢查: {check}")
                await asyncio.sleep(0.1)  # 模擬處理時間

            self.update_progress(task, 100, "基本法規檢查完成")

            return {
                "status": "completed",
                "result": "基本法規比對完成（建議使用 AI 模型進行詳細分析）",
                "compliance_score": 70,  # 保守分數
                "violations": violations,
                "recommendations": recommendations,
                "analysis_mode": "basic",
                "note": "此為基本分析模式，建議配置 Ollama 進行詳細 AI 分析"
            }

        except Exception as e:
            logger.error(f"基本法規分析失敗: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "analysis_mode": "basic"
            }
